<template>
  <div class="q-pa-md">
    <div class="text-h6">Step 1: Enter Basic Information About The Course To Generate Chatbot Information
    </div>
    <q-card>
      <q-card-section>
        <q-input v-model="systemPromptInfoForm.subject" label="Course/Subject" clearable
          placeholder='Study topic (such as "Python Programming" or "Calculus")' type="text"
          :rules="[(val) => !!val || 'Course Name is required']" />
        <q-select v-model="systemPromptInfoForm.student_level" label="Student Level" :options="typeLevelOptions" />
        <q-select v-model="systemPromptInfoForm.coach_style" label="Coach Style" :options="typeCoachStyleOptions" />

        <q-input v-model="systemPromptInfoForm.primary_learning_goals" label="Primary Learning Goals" clearable
          type="textarea"
          placeholder="What specific skills or knowledge do you want students to develop? e.g., Logical reasoning, critical thinking, etc."
          :rules="[
            (val) =>
              val && (val.split(' ').length <= 200) ||
              'Primary Learning Goals is required and limited to 200 words.',
          ]" />
        <q-input v-model="systemPromptInfoForm.Common_Challenges_and_Solutions" label="Common Challenges and Solutions"
          clearable type="textarea"
          placeholder="What common challenges do students face? How can the chatbot help students overcome these challenges? e.g., Difficulty in constructing logical arguments, lack of confidence in public speaking, etc."
          :rules="[
            (val) =>
              (val.split(' ').length <= 200) ||
              'Common Challenges and Solutions is limited to 200 words.',
          ]" />
        <q-input v-model="systemPromptInfoForm.Extra_Considerations" label="Extra Considerations" clearable
          type="textarea" placeholder="e.g. Prioritize real-world programming examples, etc." :rules="[
            (val) =>
              (val.split(' ').length <= 200) ||
              'Extra Considerations is limited to 200 words.',
          ]" />
        <q-btn icon="create" no-caps label="Generate System Prompt" color="black" text-color="white"
          style="margin-top: 15px; margin-bottom: 15px;" @click="handleGenerateSystemPrompt" />
      </q-card-section>
    </q-card>

    <q-separator spaced="15px" />
    <div class="text-h6">Step 2: Check System Prompt and Welcome Prompt
    </div>
    <q-card>
      <q-card-section>
        <q-input v-model="form.chatbot_name" label="ChatBot Name" clearable type="text" placeholder="ChatBot Name" />
        <q-input v-model="form.system_prompt" label="System Prompt" clearable type="textarea"
          placeholder="System Prompt" />
        <q-input v-model="form.welcome_prompt" label="Welcome Prompt" clearable type="textarea"
          placeholder="Welcome Prompt" />
      </q-card-section>
    </q-card>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useQuasar } from 'quasar'
// import { api } from 'src/boot/axios';

const $q = useQuasar()


const systemPromptInfoForm = ref({
  subject: '',
  student_level: 'Beginner(No prior knowledge)',
  coach_style: 'Socratic Method(Question-based learning)',
  primary_learning_goals: '',
  Common_Challenges_and_Solutions: '',
  Extra_Considerations: '',
});

const typeLevelOptions = ['Beginner(No prior knowledge)', 'Intermediate(Some prior knowledge)', 'Advanced(Extensive prior knowledge)', 'Mixed(Varied levels of knowledge)'];
const typeCoachStyleOptions = ['Socratic Method(Question-based learning)', 'Supportive & Encouraging', 'Structured & Step-by-step', 'Collaborative Discussion', 'Adaptive(Adjusts to student needs)'];


const form = ref({
  chatbot_name: '',
  system_prompt: '',
  welcome_prompt: '',
});

const handleGenerateSystemPrompt = () => {
  try {
    $q.dialog({
      title: 'Generate System Prompt',
      message: 'Are you sure you want to generate the system prompt?',
      ok: 'Yes',
      cancel: 'No',
    }).onOk(() => {
      void (async () => {
        // Input validation
        const validationRules = [
          { field: 'subject', required: true, message: 'Subject is required' },
          { field: 'student_level', required: true, message: 'Student Level is required' },
          { field: 'coach_style', required: true, message: 'Coach Style is required' },
          { field: 'primary_learning_goals', required: true, wordLimit: 200, message: 'Primary Learning Goals is required and limited to 200 words.' },
          { field: 'Common_Challenges_and_Solutions', wordLimit: 200, message: 'Common Challenges and Solutions is limited to 200 words.' },
          { field: 'Extra_Considerations', wordLimit: 200, message: 'Extra Considerations is limited to 200 words.' },
        ];

        // Trim all fields and validate
        for (const rule of validationRules) {
          const fieldValue = systemPromptInfoForm.value[rule.field];
          systemPromptInfoForm.value[rule.field] = fieldValue.trim();

          // Check required fields
          if (rule.required && !systemPromptInfoForm.value[rule.field]) {
            $q.notify({
              type: 'negative',
              message: rule.message,
            });
            return;
          }

          // Check word limit
          if (rule.wordLimit && systemPromptInfoForm.value[rule.field].split(' ').length > rule.wordLimit) {
            $q.notify({
              type: 'negative',
              message: rule.message,
            });
            return;
          }
        }

        // $q.loading.show({
        //   message: 'Generating System Prompt...',
        // });

        // const response = await api.post('/systemPromptInfo', systemPromptInfoForm.value);

        // form.value.chatbot_name = response.data.genInfo.chatbot_name;
        // form.value.system_prompt = response.data.genInfo.system_prompt;
        // form.value.welcome_prompt = response.data.genInfo.welcome_prompt;

        // 清空表单字段
        form.value.chatbot_name = '';
        form.value.welcome_prompt = '';
        form.value.system_prompt = '';

        // 发送POST请求到流式端点
        const response = await fetch('http://localhost:8000/api/systemPromptInfo/streaming', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(systemPromptInfoForm.value)
        });

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const reader = response.body.getReader();
        const decoder = new TextDecoder();
        let buffer = '';

        try {
          while (true) {
            const { done, value } = await reader.read();

            if (done) break;

            // 将新数据添加到缓冲区
            buffer += decoder.decode(value, { stream: true });

            // 按行分割数据
            const lines = buffer.split('\n');
            // 保留最后一行（可能不完整）
            buffer = lines.pop() || '';

            for (const line of lines) {
              if (line.trim()) {
                try {
                  let data;
                  // 处理SSE格式
                  if (line.startsWith('data: ')) {
                    const jsonStr = line.substring(6); // 移除 'data: '
                    if (jsonStr.trim() === '[DONE]') {
                      console.log('流式传输完成');
                      break;
                    }
                    data = JSON.parse(jsonStr);
                  } else {
                    // 直接解析JSON
                    data = JSON.parse(line);
                  }

                  // 根据数据类型更新对应字段
                  if (data.type === 'chatbot_name') {
                    if (data.delta) {
                      form.value.chatbot_name += data.delta;
                    } else if (data.content) {
                      form.value.chatbot_name = data.content;
                    }
                  } else if (data.type === 'welcome_prompt') {
                    if (data.delta) {
                      form.value.welcome_prompt += data.delta;
                    } else if (data.content) {
                      form.value.welcome_prompt = data.content;
                    }
                  } else if (data.type === 'system_prompt') {
                    if (data.delta) {
                      form.value.system_prompt += data.delta;
                    } else if (data.content) {
                      form.value.system_prompt = data.content;
                    }
                  } else if (data.type === 'complete') {
                    console.log('生成完成');
                    
                    // 如果有完整数据，直接设置
                    if (data.data) {
                      form.value.chatbot_name = data.data.chatbot_name || form.value.chatbot_name;
                      form.value.welcome_prompt = data.data.welcome_prompt || form.value.welcome_prompt;
                      form.value.system_prompt = data.data.system_prompt || form.value.system_prompt;
                    }
                  }
                } catch (parseError) {
                  console.error('解析数据错误:', parseError, '原始数据:', line);
                }
              }
            }
          }
        } finally {
          reader.releaseLock();
        }


        $q.notify({
          type: 'positive',
          message: 'System Prompt generated successfully',
        });

        // $q.loading.hide();

      })();
    });
  } catch (error) {
    $q.notify({
      type: 'negative',
      message: 'Failed to generate system prompt: ' + String(error),
    });
  }
}
</script>