{"name": "systemprompt-fe", "version": "0.0.1", "description": "systemPrompt", "productName": "systemPrompt", "author": "Barry <<EMAIL>>", "type": "module", "private": true, "scripts": {"lint": "eslint -c ./eslint.config.js \"./src*/**/*.{js,cjs,mjs,vue}\"", "format": "prettier --write \"**/*.{js,vue,scss,html,md,json}\" --ignore-path .gitignore", "test": "echo \"No test specified\" && exit 0", "dev": "quasar dev", "build": "quasar build", "postinstall": "quasar prepare"}, "dependencies": {"axios": "^1.2.1", "vue-i18n": "^11.0.0", "pinia": "^3.0.1", "@quasar/extras": "^1.16.4", "quasar": "^2.16.0", "vue": "^3.4.18", "vue-router": "^4.0.0"}, "devDependencies": {"@intlify/unplugin-vue-i18n": "^4.0.0", "@eslint/js": "^9.14.0", "eslint": "^9.14.0", "eslint-plugin-vue": "^9.30.0", "globals": "^15.12.0", "vite-plugin-checker": "^0.9.0", "@vue/eslint-config-prettier": "^10.1.0", "prettier": "^3.3.3", "@quasar/app-vite": "^2.1.0", "autoprefixer": "^10.4.2", "postcss": "^8.4.14"}, "engines": {"node": "^28 || ^26 || ^24 || ^22 || ^20 || ^18", "npm": ">= 6.13.4", "yarn": ">= 1.21.1"}}